<?php

namespace App\Http\Controllers;

use App\Models\Event;
use App\Models\Order;
use App\Models\Ticket;
use App\Models\Voucher;
use App\Services\VoucherService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use Illuminate\Support\Facades\Storage;
use Carbon\Carbon;

class TicketController extends Controller
{
    protected $voucherService;

    public function __construct(VoucherService $voucherService)
    {
        $this->middleware('auth');
        $this->voucherService = $voucherService;
    }

    /**
     * Show ticket purchase page
     */
    public function purchase(Event $event)
    {
        // Check if event allows ticket purchase
        if (!$event->canPurchaseTickets()) {
            return redirect()->route('tickets.show', $event)
                ->with('error', 'Tiket untuk event ini tidak dapat dibeli saat ini.');
        }

        // Check if user already has tickets for this event
        $existingTickets = auth()->user()->tickets()
            ->where('event_id', $event->id)
            ->where('status', '!=', 'cancelled')
            ->count();

        $maxPurchase = $event->max_purchase ?? 10;
        $remainingPurchase = max(0, $maxPurchase - $existingTickets);

        if ($remainingPurchase <= 0) {
            return redirect()->route('tickets.show', $event)
                ->with('error', 'Anda sudah mencapai batas maksimal pembelian tiket untuk event ini.');
        }

        $availableTickets = $event->available_capacity;
        $maxQuantity = min($remainingPurchase, $availableTickets, $maxPurchase);

        return view('tickets.purchase', compact('event', 'maxQuantity', 'existingTickets'));
    }

    /**
     * Process ticket purchase
     */
    public function store(Request $request, Event $event)
    {
        $validator = Validator::make($request->all(), [
            'quantity' => 'required|integer|min:1|max:' . min($event->max_purchase ?? 10, $event->available_capacity),
            'attendee_name' => 'required|string|max:255',
            'attendee_email' => 'required|email|max:255',
            'attendee_phone' => 'required|string|max:20',
            'identity_type' => 'required|in:ktp,sim,passport,kartu_pelajar,ktm,kta',
            'identity_number' => 'required|string|max:20',
            'emergency_contact_name' => 'nullable|string|max:255',
            'emergency_contact_phone' => 'nullable|string|max:20',
            'emergency_contact_relation' => 'nullable|in:orangtua,saudara,pasangan,teman,lainnya',
            'dietary_requirements' => 'nullable|array',
            'dietary_requirements.*' => 'in:vegetarian,vegan,halal,no_pork',
            'accessibility_requirements' => 'nullable|array',
            'accessibility_requirements.*' => 'in:wheelchair,hearing_impaired',
            'special_notes' => 'nullable|string|max:1000',
            'payment_method' => 'required|in:bank_transfer,credit_card,e_wallet,qris,virtual_account,cash',
            'terms_accepted' => 'required|accepted',
            'voucher_code' => 'nullable|string|max:50',
        ], [
            'quantity.required' => 'Jumlah tiket wajib diisi.',
            'quantity.max' => 'Jumlah tiket melebihi batas maksimal.',
            'attendee_name.required' => 'Nama peserta wajib diisi.',
            'attendee_email.required' => 'Email peserta wajib diisi.',
            'attendee_phone.required' => 'Nomor telepon wajib diisi.',
            'identity_type.required' => 'Jenis identitas wajib dipilih.',
            'identity_number.required' => 'Nomor identitas wajib diisi.',
            'payment_method.required' => 'Metode pembayaran wajib dipilih.',
            'terms_accepted.accepted' => 'Anda harus menyetujui syarat dan ketentuan.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Double check availability
        if (!$event->canPurchaseTickets()) {
            return back()->with('error', 'Tiket tidak dapat dibeli saat ini.')->withInput();
        }

        if ($event->available_capacity < $request->quantity) {
            return back()->with('error', 'Tiket yang tersedia tidak mencukupi.')->withInput();
        }

        try {
            DB::beginTransaction();

            // Calculate pricing
            $unitPrice = $event->price;
            $subtotal = $unitPrice * $request->quantity;
            $adminFee = $this->calculateAdminFee($subtotal);
            $discountAmount = $this->calculateDiscount($event, $request->quantity);

            // Handle voucher
            $voucherDiscount = 0;
            $appliedVoucher = null;
            if ($request->filled('voucher_code')) {
                $voucherResult = $this->voucherService->validateAndApplyVoucher(
                    $request->voucher_code,
                    $event,
                    auth()->user(),
                    $subtotal + $adminFee,
                    $request->quantity
                );

                if ($voucherResult['success']) {
                    $voucherDiscount = $voucherResult['discount'];
                    $appliedVoucher = $voucherResult['voucher'];
                }
            }

            $totalAmount = $subtotal + $adminFee - $discountAmount - $voucherDiscount;

            // Prepare attendee data
            $attendeeData = [
                'name' => $request->attendee_name,
                'email' => $request->attendee_email,
                'phone' => $request->attendee_phone,
                'identity_type' => $request->identity_type,
                'identity_number' => $request->identity_number,
                'emergency_contact' => [
                    'name' => $request->emergency_contact_name,
                    'phone' => $request->emergency_contact_phone,
                    'relation' => $request->emergency_contact_relation,
                ],
                'dietary_requirements' => $request->dietary_requirements ?? [],
                'accessibility_requirements' => $request->accessibility_requirements ?? [],
                'special_notes' => $request->special_notes,
            ];

            // Create order
            $order = Order::create([
                'order_number' => $this->generateOrderNumber(),
                'user_id' => auth()->id(),
                'event_id' => $event->id,
                'quantity' => $request->quantity,
                'unit_price' => $unitPrice,
                'subtotal' => $subtotal,
                'admin_fee' => $adminFee,
                'discount_amount' => $discountAmount,
                'voucher_id' => $appliedVoucher ? $appliedVoucher->id : null,
                'voucher_code' => $appliedVoucher ? $appliedVoucher->code : null,
                'voucher_discount' => $voucherDiscount,
                'total_amount' => $totalAmount,
                'payment_method' => $request->payment_method,
                'customer_name' => $request->attendee_name,
                'customer_email' => $request->attendee_email,
                'customer_phone' => $request->attendee_phone,
                'attendee_data' => json_encode($attendeeData),
                'expires_at' => now()->addMinutes(30), // 30 minutes to complete payment
                'status' => 'pending',
                'payment_status' => 'pending',
            ]);

            // Apply voucher to order if applicable
            if ($appliedVoucher && $voucherDiscount > 0) {
                $this->voucherService->applyVoucherToOrder($order, $appliedVoucher, $voucherDiscount);
            }

            // Create tickets
            for ($i = 0; $i < $request->quantity; $i++) {
                $ticket = Ticket::create([
                    'ticket_number' => $this->generateTicketNumber(),
                    'qr_code' => $this->generateQRCode(),
                    'event_id' => $event->id,
                    'buyer_id' => auth()->id(),
                    'order_id' => $order->id,
                    'attendee_name' => $request->attendee_name,
                    'attendee_email' => $request->attendee_email,
                    'attendee_phone' => $request->attendee_phone,
                    'attendee_data' => json_encode($attendeeData),
                    'price' => $unitPrice,
                    'admin_fee' => $adminFee / $request->quantity,
                    'total_paid' => $totalAmount / $request->quantity,
                    'status' => 'active',
                    'download_token' => Str::random(32),
                ]);

                // Generate QR Code image
                $this->generateQRCodeImage($ticket);
            }

            // Update event capacity
            $event->decrement('available_capacity', $request->quantity);

            DB::commit();

            // Redirect to payment page
            return redirect()->route('orders.payment', $order)
                ->with('success', 'Pesanan berhasil dibuat! Silakan lanjutkan pembayaran.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat memproses pesanan: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show user's tickets
     */
    public function myTickets(Request $request)
    {
        $query = auth()->user()->tickets()->with(['event', 'order']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by event
        if ($request->filled('event')) {
            $query->whereHas('event', function ($q) use ($request) {
                $q->where('title', 'like', '%' . $request->event . '%');
            });
        }

        // Sort by date
        $sortBy = $request->get('sort', 'created_at');
        $sortOrder = $request->get('order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        $tickets = $query->paginate(10)->withQueryString();

        // Get statistics
        $stats = [
            'total' => auth()->user()->tickets()->count(),
            'active' => auth()->user()->tickets()->where('status', 'active')->count(),
            'used' => auth()->user()->tickets()->where('status', 'used')->count(),
            'upcoming' => auth()->user()->tickets()
                ->whereHas('event', function ($q) {
                    $q->where('start_date', '>', now());
                })
                ->where('status', 'active')
                ->count(),
        ];

        return view('tickets.my-tickets', compact('tickets', 'stats'));
    }

    /**
     * Show ticket detail
     */
    public function show(Ticket $ticket)
    {
        // Check if user owns this ticket
        if ($ticket->buyer_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke tiket ini.');
        }

        $ticket->load(['event', 'order']);

        return view('tickets.show', compact('ticket'));
    }

    /**
     * Download ticket as PDF
     */
    public function download(Ticket $ticket)
    {
        // Check if user owns this ticket
        if ($ticket->buyer_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke tiket ini.');
        }

        // Increment download count
        $ticket->increment('download_count');
        $ticket->update(['last_downloaded_at' => now()]);

        // Generate PDF (using DomPDF or similar)
        $pdf = $this->generateTicketPDF($ticket);

        return $pdf->download("ticket-{$ticket->ticket_number}.pdf");
    }

    /**
     * Validate ticket (for staff)
     */
    public function validateTicket(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'qr_code' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['error' => 'QR Code tidak valid'], 400);
        }

        $ticket = Ticket::where('qr_code', $request->qr_code)->first();

        if (!$ticket) {
            return response()->json(['error' => 'Tiket tidak ditemukan'], 404);
        }

        // Check if ticket is already used
        if ($ticket->status === 'used') {
            return response()->json([
                'error' => 'Tiket sudah digunakan',
                'used_at' => $ticket->used_at,
                'validated_by' => $ticket->validatedBy->name ?? 'Unknown'
            ], 400);
        }

        // Check if ticket is cancelled
        if ($ticket->status === 'cancelled') {
            return response()->json(['error' => 'Tiket telah dibatalkan'], 400);
        }

        // Check if event has started
        if ($ticket->event->start_date > now()) {
            return response()->json([
                'error' => 'Event belum dimulai',
                'event_start' => $ticket->event->start_date->format('d M Y H:i')
            ], 400);
        }

        // Mark ticket as used
        $ticket->update([
            'status' => 'used',
            'used_at' => now(),
            'validated_by' => auth()->id(),
            'validation_notes' => $request->notes,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Tiket berhasil divalidasi',
            'ticket' => [
                'ticket_number' => $ticket->ticket_number,
                'attendee_name' => $ticket->attendee_name,
                'event_title' => $ticket->event->title,
                'validated_at' => $ticket->used_at->format('d M Y H:i'),
            ]
        ]);
    }

    /**
     * Cancel ticket
     */
    public function cancel(Ticket $ticket, Request $request)
    {
        // Check if user owns this ticket
        if ($ticket->buyer_id !== auth()->id()) {
            abort(403, 'Anda tidak memiliki akses ke tiket ini.');
        }

        // Check if ticket can be cancelled
        if ($ticket->status !== 'active') {
            return back()->with('error', 'Tiket tidak dapat dibatalkan.');
        }

        // Check cancellation policy (e.g., 24 hours before event)
        $cancellationDeadline = $ticket->event->start_date->subHours(24);
        if (now() > $cancellationDeadline) {
            return back()->with('error', 'Tiket tidak dapat dibatalkan kurang dari 24 jam sebelum event.');
        }

        $validator = Validator::make($request->all(), [
            'reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        try {
            DB::beginTransaction();

            // Update ticket status
            $ticket->update([
                'status' => 'cancelled',
                'cancellation_reason' => $request->reason,
                'cancelled_at' => now(),
            ]);

            // Update event capacity
            $ticket->event->increment('available_capacity');

            // Process refund (if applicable)
            $this->processRefund($ticket);

            DB::commit();

            return back()->with('success', 'Tiket berhasil dibatalkan. Refund akan diproses dalam 3-5 hari kerja.');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->with('error', 'Terjadi kesalahan saat membatalkan tiket: ' . $e->getMessage());
        }
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(Str::random(5));
        } while (Order::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Generate unique ticket number
     */
    private function generateTicketNumber(): string
    {
        do {
            $ticketNumber = 'TIK-' . date('Ymd') . '-' . strtoupper(Str::random(6));
        } while (Ticket::where('ticket_number', $ticketNumber)->exists());

        return $ticketNumber;
    }

    /**
     * Generate unique QR code
     */
    private function generateQRCode(): string
    {
        do {
            $qrCode = 'QR-' . Str::random(20);
        } while (Ticket::where('qr_code', $qrCode)->exists());

        return $qrCode;
    }

    /**
     * Generate QR code image
     */
    private function generateQRCodeImage(Ticket $ticket): void
    {
        $qrCodeData = json_encode([
            'ticket_id' => $ticket->id,
            'ticket_number' => $ticket->ticket_number,
            'qr_code' => $ticket->qr_code,
            'event_id' => $ticket->event_id,
            'attendee_name' => $ticket->attendee_name,
            'generated_at' => now()->toISOString(),
        ]);

        $qrCodeImage = QrCode::format('png')
            ->size(300)
            ->margin(2)
            ->generate($qrCodeData);

        $filename = "qr-codes/{$ticket->ticket_number}.png";
        Storage::disk('public')->put($filename, $qrCodeImage);

        $ticket->update(['qr_code_path' => $filename]);
    }

    /**
     * Calculate admin fee
     */
    private function calculateAdminFee(float $subtotal): float
    {
        // 5% admin fee with minimum Rp 2,500 and maximum Rp 50,000
        $fee = $subtotal * 0.05;
        return max(2500, min(50000, $fee));
    }

    /**
     * Calculate discount
     */
    private function calculateDiscount(Event $event, int $quantity): float
    {
        // Implement discount logic here
        // For example: bulk discount, promo codes, etc.
        return 0;
    }

    /**
     * Generate ticket PDF
     */
    private function generateTicketPDF(Ticket $ticket)
    {
        // Implementation would use DomPDF or similar
        // For now, return a placeholder
        return response()->json(['message' => 'PDF generation not implemented yet']);
    }

    /**
     * Process refund
     */
    private function processRefund(Ticket $ticket): void
    {
        // Implementation would integrate with payment gateway
        // For simulation, just log the refund request
        \Log::info("Refund requested for ticket {$ticket->ticket_number}");
    }
}
