<?php

/**
 * Force GD Driver Bootstrap
 * 
 * This file ensures that the application always uses GD driver
 * for image processing, preventing any Imagick-related errors.
 */

// Force environment variables
if (!defined('IMAGE_DRIVER_FORCED')) {
    putenv('IMAGE_DRIVER=gd');
    $_ENV['IMAGE_DRIVER'] = 'gd';
    $_SERVER['IMAGE_DRIVER'] = 'gd';
    
    define('IMAGE_DRIVER_FORCED', true);
}

// Override any configuration that might try to use Imagick
if (function_exists('config')) {
    config(['image.driver' => 'gd']);
    config(['intervention.driver' => \Intervention\Image\Drivers\Gd\Driver::class]);
}

// Log the driver being used for debugging
if (function_exists('logger')) {
    logger('Image driver forced to GD for compatibility');
}
