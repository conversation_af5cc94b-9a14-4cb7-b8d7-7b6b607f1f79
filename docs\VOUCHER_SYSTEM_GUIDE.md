# 🎫 Voucher System Guide - TikPro

## 📋 Overview

Sistem voucher TikPro memungkinkan admin untuk membuat dan mengelola voucher diskon yang dapat digunakan oleh pengguna untuk mendapatkan potongan harga saat membeli tiket event.

## ✨ Fitur Utama

### 🔧 **Admin Features**
- ✅ **Voucher Management**: Buat, edit, hapus, dan kelola voucher
- ✅ **Flexible Discount Types**: Persentase atau nominal tetap
- ✅ **Usage Limits**: Batasi penggunaan total dan per user
- ✅ **Event/Category Restrictions**: Terapkan voucher untuk event atau kategori tertentu
- ✅ **User Role Restrictions**: Batasi voucher untuk role tertentu
- ✅ **Validity Period**: Set tanggal mulai dan berakhir
- ✅ **Usage Analytics**: Lihat statistik penggunaan voucher

### 👤 **User Features**
- ✅ **Easy Application**: Input kode voucher di ringkasan pesanan
- ✅ **Real-time Validation**: Validasi voucher secara real-time
- ✅ **Instant Feedback**: Lihat diskon yang diterapkan langsung
- ✅ **Error Handling**: Pesan error yang jelas jika voucher tidak valid

## 🗂 Database Structure

### **Vouchers Table**
```sql
- id (Primary Key)
- code (Unique voucher code)
- name (Display name)
- description (Optional description)
- type (percentage/fixed)
- value (Discount value)
- min_order_amount (Minimum order amount)
- max_discount_amount (Maximum discount for percentage type)
- usage_limit (Total usage limit)
- usage_limit_per_user (Usage limit per user)
- used_count (Current usage count)
- starts_at (Start date)
- expires_at (End date)
- applicable_events (JSON array of event IDs)
- applicable_categories (JSON array of category IDs)
- applicable_user_roles (JSON array of user roles)
- min_tickets (Minimum tickets required)
- max_tickets (Maximum tickets allowed)
- is_active (Active status)
- is_public (Public visibility)
- created_by_type (Creator type)
- created_by_id (Creator ID)
- metadata (Additional data)
```

### **Voucher Usages Table**
```sql
- id (Primary Key)
- voucher_id (Foreign Key to vouchers)
- user_id (Foreign Key to users)
- order_id (Foreign Key to orders)
- event_id (Foreign Key to events)
- original_amount (Original order amount)
- discount_amount (Applied discount)
- final_amount (Final amount after discount)
- tickets_quantity (Number of tickets)
- voucher_snapshot (Voucher data at time of use)
- applied_via (How voucher was applied)
- used_at (Usage timestamp)
```

### **Orders Table Updates**
```sql
- voucher_id (Foreign Key to vouchers)
- voucher_discount (Voucher discount amount)
- voucher_code (Applied voucher code)
```

## 🚀 Usage Guide

### **For Admin**

#### 1. **Creating Vouchers**
1. Go to Admin Dashboard → Vouchers
2. Click "Tambah Voucher"
3. Fill in voucher details:
   - **Code**: Unique voucher code (e.g., WELCOME10)
   - **Name**: Display name
   - **Type**: Percentage or Fixed amount
   - **Value**: Discount value
   - **Restrictions**: Set minimum order, usage limits, etc.
   - **Validity**: Set start and end dates
4. Save voucher

#### 2. **Managing Vouchers**
- **View All**: See all vouchers with status and usage statistics
- **Edit**: Modify voucher details
- **Toggle Status**: Activate/deactivate vouchers
- **Delete**: Remove unused vouchers
- **Analytics**: View usage statistics and performance

#### 3. **Voucher Types**

**Percentage Discount:**
```
Type: percentage
Value: 10 (for 10% discount)
Max Discount: 100000 (maximum Rp 100,000 discount)
```

**Fixed Amount Discount:**
```
Type: fixed
Value: 50000 (for Rp 50,000 discount)
```

### **For Users**

#### 1. **Applying Vouchers**
1. Go to ticket purchase page
2. Fill in attendee details
3. In "Ringkasan Pesanan" section, find "Kode Voucher"
4. Enter voucher code
5. Click "Terapkan"
6. See discount applied in price breakdown

#### 2. **Voucher Validation**
- ✅ **Valid**: Green success message with discount amount
- ❌ **Invalid**: Red error message with reason
- ⏳ **Loading**: Spinner while validating

## 🎯 Sample Vouchers

The system comes with pre-seeded sample vouchers:

| Code | Type | Value | Description | Status |
|------|------|-------|-------------|--------|
| `WELCOME10` | Percentage | 10% | Welcome discount for new users | ✅ Active |
| `NEWYEAR2024` | Percentage | 25% | New Year special discount | ✅ Active |
| `EARLYBIRD` | Fixed | Rp 50,000 | Early bird discount | ✅ Active |
| `STUDENT15` | Percentage | 15% | Student discount | ✅ Active |
| `BULK20` | Percentage | 20% | Bulk purchase discount | ✅ Active |
| `WEEKEND50K` | Fixed | Rp 50,000 | Weekend special | ✅ Active |
| `EXPIRED10` | Percentage | 10% | Expired voucher (for testing) | ❌ Expired |
| `INACTIVE20` | Percentage | 20% | Inactive voucher (for testing) | ❌ Inactive |

## 🔧 API Endpoints

### **Voucher Validation**
```http
POST /vouchers/validate
Content-Type: application/json

{
    "voucher_code": "WELCOME10",
    "event_id": 1,
    "order_amount": 100000,
    "ticket_quantity": 2
}
```

**Response:**
```json
{
    "success": true,
    "message": "Voucher berhasil diterapkan!",
    "voucher": {
        "id": 1,
        "code": "WELCOME10",
        "name": "Welcome Discount 10%",
        "type": "percentage",
        "formatted_value": "10%"
    },
    "discount": 10000,
    "final_amount": 90000,
    "savings_percentage": 10
}
```

### **Available Vouchers**
```http
GET /vouchers/available?event_id=1&order_amount=100000&ticket_quantity=2
```

### **Voucher Details**
```http
GET /vouchers/{code}
```

## 🛠 Technical Implementation

### **VoucherService Class**
Main service class handling voucher logic:
- `validateAndApplyVoucher()`: Validate and calculate discount
- `applyVoucherToOrder()`: Apply voucher to order
- `getAvailableVouchers()`: Get available vouchers for user/event
- `removeVoucherFromOrder()`: Remove voucher from order

### **Frontend Integration**
- **Alpine.js**: Real-time voucher application
- **AJAX**: Asynchronous voucher validation
- **Dynamic UI**: Real-time price updates

### **Validation Rules**
1. **Voucher Existence**: Check if voucher exists
2. **Active Status**: Check if voucher is active
3. **Validity Period**: Check start and end dates
4. **Usage Limits**: Check total and per-user limits
5. **Event/Category Restrictions**: Check applicability
6. **User Role Restrictions**: Check user eligibility
7. **Order Requirements**: Check minimum order amount and ticket quantity

## 🎨 UI/UX Features

### **Purchase Form Integration**
- **Voucher Section**: Dedicated section in order summary
- **Real-time Validation**: Instant feedback on voucher application
- **Visual Feedback**: Success/error states with appropriate colors
- **Price Breakdown**: Clear display of original price, discount, and final price

### **Admin Dashboard**
- **Statistics Cards**: Overview of voucher performance
- **Filter & Search**: Easy voucher management
- **Status Indicators**: Visual status representation
- **Usage Progress**: Progress bars for usage limits

## 🔍 Testing

### **Test Scenarios**
1. **Valid Voucher**: Apply valid voucher and verify discount
2. **Invalid Voucher**: Test with non-existent voucher code
3. **Expired Voucher**: Test with expired voucher
4. **Usage Limit**: Test voucher usage limits
5. **Minimum Order**: Test minimum order amount validation
6. **Event Restrictions**: Test event-specific vouchers
7. **User Restrictions**: Test user role restrictions

### **Test Vouchers**
Use the pre-seeded vouchers for testing different scenarios.

## 📊 Analytics & Reporting

### **Voucher Performance Metrics**
- Total vouchers created
- Active vouchers count
- Total usage count
- Total savings provided
- Most popular vouchers
- Usage trends over time

### **Individual Voucher Analytics**
- Usage count vs limit
- Unique users count
- Average discount amount
- Usage by day/week/month
- Conversion rate

## 🚨 Error Handling

### **Common Error Messages**
- "Kode voucher tidak ditemukan"
- "Voucher sudah kedaluwarsa"
- "Voucher tidak aktif"
- "Anda sudah mencapai batas penggunaan voucher ini"
- "Voucher tidak berlaku untuk event ini"
- "Minimum order amount is Rp X"

### **Error Recovery**
- Clear error messages
- Suggestions for valid vouchers
- Easy voucher removal
- Form state preservation

## 🔐 Security Considerations

1. **Input Validation**: Sanitize voucher codes
2. **Rate Limiting**: Prevent voucher code brute force
3. **Usage Tracking**: Monitor suspicious usage patterns
4. **Admin Access**: Restrict voucher management to admins
5. **Audit Trail**: Log all voucher operations

## 🎯 Future Enhancements

- **Auto-apply**: Automatically apply best available voucher
- **Voucher Recommendations**: Suggest vouchers to users
- **Bulk Voucher Generation**: Generate multiple vouchers at once
- **Advanced Analytics**: More detailed reporting and insights
- **Voucher Campaigns**: Marketing campaign integration
- **Social Sharing**: Share voucher codes on social media

---

## 📞 Support

For technical support or questions about the voucher system, please contact the development team or refer to the main documentation.

**Last Updated**: December 2024  
**Version**: 1.0.0
