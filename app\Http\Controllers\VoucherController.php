<?php

namespace App\Http\Controllers;

use App\Models\Voucher;
use App\Models\Event;
use App\Services\VoucherService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class VoucherController extends Controller
{
    protected $voucherService;

    public function __construct(VoucherService $voucherService)
    {
        $this->middleware('auth');
        $this->voucherService = $voucherService;
    }

    /**
     * Validate voucher for purchase
     */
    public function validate(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'voucher_code' => 'required|string|max:50',
            'event_id' => 'required|exists:events,id',
            'order_amount' => 'required|numeric|min:0',
            'ticket_quantity' => 'required|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $event = Event::findOrFail($request->event_id);
            $user = auth()->user();

            $result = $this->voucherService->validateAndApplyVoucher(
                $request->voucher_code,
                $event,
                $user,
                $request->order_amount,
                $request->ticket_quantity
            );

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'voucher' => [
                        'id' => $result['voucher']->id,
                        'code' => $result['voucher']->code,
                        'name' => $result['voucher']->name,
                        'type' => $result['voucher']->type,
                        'formatted_value' => $result['voucher']->formatted_value,
                    ],
                    'discount' => $result['discount'],
                    'final_amount' => $result['final_amount'],
                    'savings_percentage' => $result['savings_percentage'] ?? 0,
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message'],
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memvalidasi voucher.',
            ], 500);
        }
    }

    /**
     * Get available vouchers for user and event
     */
    public function available(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'event_id' => 'required|exists:events,id',
            'order_amount' => 'nullable|numeric|min:0',
            'ticket_quantity' => 'nullable|integer|min:1',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Data tidak valid.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $event = Event::findOrFail($request->event_id);
            $user = auth()->user();
            $orderAmount = $request->get('order_amount', 0);
            $ticketQuantity = $request->get('ticket_quantity', 1);

            $availableVouchers = $this->voucherService->getAvailableVouchers(
                $event,
                $user,
                $orderAmount,
                $ticketQuantity
            );

            $formattedVouchers = array_map(function ($item) {
                return [
                    'voucher' => [
                        'id' => $item['voucher']->id,
                        'code' => $item['voucher']->code,
                        'name' => $item['voucher']->name,
                        'description' => $item['voucher']->description,
                        'type' => $item['voucher']->type,
                        'formatted_value' => $item['voucher']->formatted_value,
                        'min_order_amount' => $item['voucher']->min_order_amount,
                        'expires_at' => $item['voucher']->expires_at->format('Y-m-d H:i:s'),
                    ],
                    'discount' => $item['discount'],
                    'final_amount' => $item['final_amount'],
                    'savings_percentage' => $item['savings_percentage'],
                ];
            }, $availableVouchers);

            return response()->json([
                'success' => true,
                'vouchers' => $formattedVouchers,
                'count' => count($formattedVouchers),
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil voucher.',
            ], 500);
        }
    }

    /**
     * Get voucher details by code
     */
    public function show(Request $request, $code)
    {
        try {
            $voucher = Voucher::where('code', strtoupper($code))
                             ->active()
                             ->public()
                             ->first();

            if (!$voucher) {
                return response()->json([
                    'success' => false,
                    'message' => 'Voucher tidak ditemukan atau tidak aktif.',
                ], 404);
            }

            $user = auth()->user();
            $userUsageCount = $voucher->usages()->where('user_id', $user->id)->count();

            return response()->json([
                'success' => true,
                'voucher' => [
                    'id' => $voucher->id,
                    'code' => $voucher->code,
                    'name' => $voucher->name,
                    'description' => $voucher->description,
                    'type' => $voucher->type,
                    'value' => $voucher->value,
                    'formatted_value' => $voucher->formatted_value,
                    'min_order_amount' => $voucher->min_order_amount,
                    'max_discount_amount' => $voucher->max_discount_amount,
                    'usage_limit' => $voucher->usage_limit,
                    'usage_limit_per_user' => $voucher->usage_limit_per_user,
                    'used_count' => $voucher->used_count,
                    'remaining_usage' => $voucher->remaining_usage,
                    'user_usage_count' => $userUsageCount,
                    'user_remaining_usage' => max(0, $voucher->usage_limit_per_user - $userUsageCount),
                    'starts_at' => $voucher->starts_at->format('Y-m-d H:i:s'),
                    'expires_at' => $voucher->expires_at->format('Y-m-d H:i:s'),
                    'status' => $voucher->status,
                    'is_valid' => $voucher->isValid(),
                    'is_available' => $voucher->isAvailable(),
                    'can_be_used_by_user' => $voucher->canBeUsedBy($user),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil detail voucher.',
            ], 500);
        }
    }

    /**
     * Get user's voucher usage history
     */
    public function userUsage(Request $request)
    {
        try {
            $user = auth()->user();
            $perPage = $request->get('per_page', 10);

            $usages = $user->voucherUsages()
                          ->with(['voucher', 'event', 'order'])
                          ->orderBy('used_at', 'desc')
                          ->paginate($perPage);

            return response()->json([
                'success' => true,
                'usages' => $usages->items(),
                'pagination' => [
                    'current_page' => $usages->currentPage(),
                    'last_page' => $usages->lastPage(),
                    'per_page' => $usages->perPage(),
                    'total' => $usages->total(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengambil riwayat penggunaan voucher.',
            ], 500);
        }
    }
}
