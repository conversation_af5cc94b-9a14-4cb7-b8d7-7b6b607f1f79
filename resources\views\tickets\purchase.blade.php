@extends('layouts.main')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-light via-white to-green-light/30">

    <!-- Header -->
    <section class="pt-8 pb-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Breadcrumb -->
            <nav class="flex mb-6" aria-label="Breadcrumb" data-aos="fade-right">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="{{ route('home') }}" class="text-gray-500 hover:text-primary transition-colors duration-200">
                            Beranda
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <a href="{{ route('tickets.show', $event) }}" class="ml-1 text-gray-500 hover:text-primary transition-colors duration-200 md:ml-2">{{ $event->title }}</a>
                        </div>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"/>
                            </svg>
                            <span class="ml-1 text-gray-700 md:ml-2">Beli Tiket</span>
                        </div>
                    </li>
                </ol>
            </nav>

            <div class="text-center mb-8">
                <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4" data-aos="fade-up">
                    Beli Tiket Event
                </h1>
                <p class="text-lg text-gray-600" data-aos="fade-up" data-aos-delay="100">
                    Lengkapi informasi di bawah untuk membeli tiket
                </p>
            </div>
        </div>
    </section>

    <!-- Purchase Form -->
    <section class="pb-16">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Error Messages -->
            @if($errors->any())
                <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <h3 class="text-sm font-semibold text-red-800 mb-2">Terjadi kesalahan:</h3>
                            <ul class="text-sm text-red-700 space-y-1">
                                @foreach($errors->all() as $error)
                                    <li>• {{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Success Messages -->
            @if(session('success'))
                <div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-green-700">{{ session('success') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Warning Messages -->
            @if(session('error'))
                <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6" data-aos="fade-up">
                    <div class="flex items-start">
                        <svg class="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        <div>
                            <p class="text-sm text-red-700">{{ session('error') }}</p>
                        </div>
                    </div>
                </div>
            @endif

            <form method="POST" action="{{ route('tickets.store', $event) }}"
                  x-data="ticketPurchase()"
                  @submit="handleSubmit"
                  novalidate>
                @csrf

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">

                    <!-- Main Form -->
                    <div class="lg:col-span-2 space-y-6">

                        <!-- Event Summary -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Detail Event</h2>
                            <div class="flex items-start space-x-4">
                                <img src="{{ $event->poster_url }}"
                                     alt="{{ $event->title }}"
                                     class="w-20 h-20 object-cover rounded-lg">
                                <div class="flex-1">
                                    <h3 class="font-semibold text-gray-900 mb-2">{{ $event->title }}</h3>
                                    <div class="space-y-1 text-sm text-gray-600">
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                            </svg>
                                            {{ $event->start_date->format('d M Y, H:i') }} WIB
                                        </div>
                                        <div class="flex items-center">
                                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            {{ $event->venue_name }}, {{ $event->city }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quantity Selection -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="100">
                            <h2 class="text-xl font-bold text-gray-900 mb-4">Jumlah Tiket</h2>

                            <div class="mb-4">
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    Pilih Jumlah Tiket
                                </label>
                                <div class="flex items-center space-x-4">
                                    <button type="button"
                                            @click="decreaseQuantity()"
                                            :disabled="quantity <= 1"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/>
                                        </svg>
                                    </button>

                                    <div class="flex-1 text-center">
                                        <input type="number"
                                               name="quantity"
                                               x-model="quantity"
                                               min="1"
                                               max="{{ $maxQuantity }}"
                                               class="w-20 text-center text-2xl font-bold border-0 focus:ring-0 focus:outline-none">
                                        <p class="text-sm text-gray-500 mt-1">
                                            Maksimal {{ $maxQuantity }} tiket
                                        </p>
                                    </div>

                                    <button type="button"
                                            @click="increaseQuantity()"
                                            :disabled="quantity >= maxQuantity"
                                            class="w-12 h-12 rounded-full border-2 border-gray-300 flex items-center justify-center hover:border-primary transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            @if($existingTickets > 0)
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <p class="text-sm text-blue-700">
                                            Anda sudah memiliki {{ $existingTickets }} tiket untuk event ini.
                                        </p>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Attendee Information -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="200">
                            <h2 class="text-xl font-bold text-gray-900 mb-6 flex items-center">
                                <svg class="w-6 h-6 mr-3 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                Informasi Peserta
                            </h2>

                            <!-- Personal Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                    Data Pribadi
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="attendee_name" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nama Lengkap <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               id="attendee_name"
                                               name="attendee_name"
                                               value="{{ old('attendee_name', auth()->user()->name) }}"
                                               required
                                               placeholder="Masukkan nama lengkap sesuai identitas"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('attendee_name')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="attendee_email" class="block text-sm font-medium text-gray-700 mb-2">
                                            Email <span class="text-red-500">*</span>
                                        </label>
                                        <input type="email"
                                               id="attendee_email"
                                               name="attendee_email"
                                               value="{{ old('attendee_email', auth()->user()->email) }}"
                                               required
                                               placeholder="<EMAIL>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('attendee_email')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="attendee_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Whatsapp <span class="text-red-500">*</span>
                                        </label>
                                        <input type="tel"
                                               id="attendee_phone"
                                               name="attendee_phone"
                                               value="{{ old('attendee_phone', auth()->user()->phone) }}"
                                               required
                                               placeholder="08xxxxxxxxxx"
                                               pattern="[0-9]{10,13}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('attendee_phone')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="attendee_gender" class="block text-sm font-medium text-gray-700 mb-2">
                                            Jenis Kelamin <span class="text-red-500">*</span>
                                        </label>
                                        <select id="attendee_gender"
                                                name="attendee_gender"
                                                required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                            <option value="">Pilih jenis kelamin</option>
                                            <option value="male" {{ old('attendee_gender') == 'male' ? 'selected' : '' }}>Laki-laki</option>
                                            <option value="female" {{ old('attendee_gender') == 'female' ? 'selected' : '' }}>Perempuan</option>
                                        </select>
                                        @error('attendee_gender')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="attendee_birth_date" class="block text-sm font-medium text-gray-700 mb-2">
                                            Tanggal Lahir <span class="text-red-500">*</span>
                                        </label>
                                        <input type="date"
                                               id="attendee_birth_date"
                                               name="attendee_birth_date"
                                               value="{{ old('attendee_birth_date') }}"
                                               required
                                               max="{{ date('Y-m-d', strtotime('-10 years')) }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('attendee_birth_date')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="attendee_occupation" class="block text-sm font-medium text-gray-700 mb-2">
                                            Pekerjaan
                                        </label>
                                        <input type="text"
                                               id="attendee_occupation"
                                               name="attendee_occupation"
                                               value="{{ old('attendee_occupation') }}"
                                               placeholder="Contoh: Mahasiswa, Karyawan, Wiraswasta"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('attendee_occupation')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Identity Information -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2"/>
                                    </svg>
                                    Identitas Diri
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="identity_type" class="block text-sm font-medium text-gray-700 mb-2">
                                            Jenis Identitas <span class="text-red-500">*</span>
                                        </label>
                                        <select id="identity_type"
                                                name="identity_type"
                                                required
                                                x-model="identityType"
                                                @change="updateIdentityPlaceholder()"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                            <option value="">Pilih jenis identitas</option>
                                            <option value="ktp" {{ old('identity_type') == 'ktp' ? 'selected' : '' }}>KTP (Kartu Tanda Penduduk)</option>
                                            <option value="sim" {{ old('identity_type') == 'sim' ? 'selected' : '' }}>SIM (Surat Izin Mengemudi)</option>
                                            <option value="passport" {{ old('identity_type') == 'passport' ? 'selected' : '' }}>Passport</option>
                                            <option value="kartu_pelajar" {{ old('identity_type') == 'kartu_pelajar' ? 'selected' : '' }}>Kartu Pelajar</option>
                                            <option value="ktm" {{ old('identity_type') == 'ktm' ? 'selected' : '' }}>KTM (Kartu Tanda Mahasiswa)</option>
                                            <option value="kta" {{ old('identity_type') == 'kta' ? 'selected' : '' }}>KTA (Kartu Tanda Anggota)</option>
                                        </select>
                                        @error('identity_type')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="identity_number" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Identitas <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text"
                                               id="identity_number"
                                               name="identity_number"
                                               value="{{ old('identity_number') }}"
                                               required
                                               x-bind:placeholder="identityPlaceholder"
                                               x-bind:pattern="identityPattern"
                                               x-bind:maxlength="identityMaxLength"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200 font-mono">
                                        @error('identity_number')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                        <p class="text-xs text-gray-500 mt-1" x-text="identityHint"></p>
                                    </div>
                                </div>

                                <!-- Identity Type Information -->
                                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-blue-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <div class="text-sm text-blue-800">
                                            <p class="font-semibold mb-2">Informasi Penting:</p>
                                            <ul class="space-y-1 text-xs">
                                                <li>• Pastikan data identitas sesuai dengan dokumen asli</li>
                                                <li>• Bawa dokumen identitas saat menghadiri event</li>
                                                <li>• Data akan digunakan untuk verifikasi saat check-in</li>
                                                <li>• Untuk keamanan, data identitas akan dienkripsi</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Emergency Contact -->
                            <div class="mb-8">
                                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                                    <svg class="w-5 h-5 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    Kontak Darurat (Opsional)
                                </h3>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div>
                                        <label for="emergency_contact_name" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nama Kontak Darurat
                                        </label>
                                        <input type="text"
                                               id="emergency_contact_name"
                                               name="emergency_contact_name"
                                               value="{{ old('emergency_contact_name') }}"
                                               placeholder="Nama keluarga/teman terdekat"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('emergency_contact_name')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>

                                    <div>
                                        <label for="emergency_contact_phone" class="block text-sm font-medium text-gray-700 mb-2">
                                            Nomor Kontak Darurat
                                        </label>
                                        <input type="tel"
                                               id="emergency_contact_phone"
                                               name="emergency_contact_phone"
                                               value="{{ old('emergency_contact_phone') }}"
                                               placeholder="08xxxxxxxxxx"
                                               pattern="[0-9]{10,13}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20 transition-all duration-200">
                                        @error('emergency_contact_phone')
                                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                                        @enderror
                                    </div>
                                </div>

                                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div class="flex items-start">
                                        <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"/>
                                        </svg>
                                        <div class="text-sm text-yellow-800">
                                            <p class="font-semibold mb-1">Kontak darurat akan dihubungi jika:</p>
                                            <ul class="space-y-1 text-xs">
                                                <li>• Terjadi keadaan darurat selama event</li>
                                                <li>• Anda tidak dapat dihubungi</li>
                                                <li>• Diperlukan konfirmasi medis</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Payment Method -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="300">
                            <h2 class="text-xl font-bold text-gray-900 mb-6">Metode Pembayaran</h2>

                            <!-- Popular Methods -->
                            <div class="mb-6">
                                <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                                    <svg class="w-4 h-4 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                    </svg>
                                    Metode Populer
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                    <!-- Bank Transfer -->
                                    <label class="relative">
                                        <input type="radio"
                                               name="payment_method"
                                               value="bank_transfer"
                                               class="sr-only peer"
                                               {{ old('payment_method', 'bank_transfer') == 'bank_transfer' ? 'checked' : '' }}>
                                        <div class="relative p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                            <div class="absolute -top-2 -right-2">
                                                <span class="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                                                    Populer
                                                </span>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                                    </svg>
                                                </div>
                                                <div class="flex-1">
                                                    <h3 class="font-semibold text-gray-900">Transfer Bank</h3>
                                                    <p class="text-sm text-gray-600">BCA, Mandiri, BNI, BRI</p>
                                                    <p class="text-xs text-green-600 font-semibold">Gratis</p>
                                                </div>
                                            </div>
                                        </div>
                                    </label>

                                    <!-- QRIS -->
                                    <label class="relative">
                                        <input type="radio"
                                               name="payment_method"
                                               value="qris"
                                               class="sr-only peer"
                                               {{ old('payment_method') == 'qris' ? 'checked' : '' }}>
                                        <div class="relative p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                            <div class="absolute -top-2 -right-2">
                                                <span class="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs px-2 py-1 rounded-full font-semibold">
                                                    Populer
                                                </span>
                                            </div>
                                            <div class="flex items-center space-x-3">
                                                <div class="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"/>
                                                    </svg>
                                                </div>
                                                <div class="flex-1">
                                                    <h3 class="font-semibold text-gray-900">QRIS</h3>
                                                    <p class="text-sm text-gray-600">Scan QR dengan semua app</p>
                                                    <p class="text-xs text-gray-500">Biaya: 0.7%</p>
                                                </div>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <!-- All Payment Methods -->
                            <div class="space-y-3">
                                <h3 class="text-sm font-semibold text-gray-700 mb-3">Semua Metode Pembayaran</h3>

                                <!-- E-Wallet -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="e_wallet"
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'e_wallet' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">E-Wallet</h3>
                                                    <p class="text-sm text-gray-600">GoPay, OVO, DANA, LinkAja, ShopeePay</p>
                                                    <p class="text-xs text-gray-500">Proses: Instan • Biaya: 1.5%</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Credit Card -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="credit_card"
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'credit_card' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Kartu Kredit/Debit</h3>
                                                    <p class="text-sm text-gray-600">Visa, Mastercard, JCB, American Express</p>
                                                    <p class="text-xs text-gray-500">Proses: Instan • Biaya: 2.9%</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Virtual Account -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="virtual_account"
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'virtual_account' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Virtual Account</h3>
                                                    <p class="text-sm text-gray-600">ATM, Mobile Banking, Internet Banking</p>
                                                    <p class="text-xs text-gray-500">Proses: 1-3 jam • Biaya: Rp 4,000</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>

                                <!-- Cash Payment -->
                                <label class="relative">
                                    <input type="radio"
                                           name="payment_method"
                                           value="cash"
                                           class="sr-only peer"
                                           {{ old('payment_method') == 'cash' ? 'checked' : '' }}>
                                    <div class="p-4 border-2 border-gray-200 rounded-xl cursor-pointer peer-checked:border-primary peer-checked:bg-primary/5 transition-all duration-200 hover:border-gray-300">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h3 class="font-semibold text-gray-900">Bayar di Tempat</h3>
                                                    <p class="text-sm text-gray-600">Bayar saat check-in event</p>
                                                    <p class="text-xs text-green-600 font-semibold">Gratis</p>
                                                </div>
                                            </div>
                                            <div class="w-5 h-5 border-2 border-gray-300 rounded-full peer-checked:border-primary peer-checked:bg-primary flex items-center justify-center">
                                                <div class="w-2 h-2 bg-white rounded-full opacity-0 peer-checked:opacity-100"></div>
                                            </div>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            @error('payment_method')
                                <p class="text-red-500 text-sm mt-4">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Terms & Conditions -->
                        <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-up" data-aos-delay="400">
                            <label class="flex items-start space-x-3">
                                <input type="checkbox"
                                       name="terms_accepted"
                                       value="1"
                                       required
                                       class="mt-1 rounded border-gray-300 text-primary focus:ring-primary">
                                <div class="text-sm text-gray-600">
                                    Saya menyetujui <a href="#" class="text-primary hover:text-accent">syarat dan ketentuan</a>
                                    serta <a href="#" class="text-primary hover:text-accent">kebijakan privasi</a> TiXara.
                                </div>
                            </label>
                            @error('terms_accepted')
                                <p class="text-red-500 text-sm mt-2">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>

                    <!-- Order Summary -->
                    <div class="lg:col-span-1">
                        <div class="sticky top-24">
                            <div class="bg-white rounded-2xl shadow-lg p-6" data-aos="fade-left">
                                <h2 class="text-xl font-bold text-gray-900 mb-6">Ringkasan Pesanan</h2>

                                <!-- Price Breakdown -->
                                <div class="space-y-4 mb-6">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Harga tiket</span>
                                        <span x-text="formatPrice({{ $event->price }})" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Jumlah</span>
                                        <span x-text="quantity + ' tiket'" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Subtotal</span>
                                        <span x-text="formatPrice(subtotal)" class="font-semibold"></span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Biaya admin</span>
                                        <span x-text="formatPrice(adminFee)" class="font-semibold"></span>
                                    </div>
                                    <div class="border-t border-gray-200 pt-4">
                                        <div class="flex justify-between items-center">
                                            <span class="text-lg font-bold text-gray-900">Total</span>
                                            <span x-text="formatPrice(total)" class="text-lg font-bold text-primary"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Purchase Button -->
                                <button type="submit"
                                        :disabled="loading"
                                        :class="loading ? 'opacity-75 cursor-not-allowed' : 'hover:shadow-lg transform hover:scale-105'"
                                        class="w-full bg-gradient-to-r from-primary to-secondary text-white py-4 rounded-xl font-bold text-lg transition-all duration-300">
                                    <span x-show="!loading" class="flex items-center justify-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0L4 5H2m5 8h10m0 0v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6z"/>
                                        </svg>
                                        Beli Tiket Sekarang
                                    </span>
                                    <span x-show="loading" class="flex items-center justify-center">
                                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Memproses Pesanan...
                                    </span>
                                </button>

                                <!-- Additional Info -->
                                <div class="mt-4 text-center">
                                    <p class="text-sm text-gray-600">
                                        <svg class="w-4 h-4 inline mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        Pesanan akan berlaku selama 30 menit untuk pembayaran
                                    </p>
                                </div>

                                <!-- Security Info -->
                                <div class="mt-6 pt-6 border-t border-gray-200">
                                    <div class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"/>
                                        </svg>
                                        Transaksi aman & terenkripsi
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function ticketPurchase() {
    return {
        quantity: 1,
        loading: false,
        eventPrice: {{ $event->price }},
        maxQuantity: {{ $maxQuantity }},
        selectedPaymentMethod: '{{ old('payment_method', 'bank_transfer') }}',
        identityType: '{{ old('identity_type', '') }}',
        identityPlaceholder: 'Pilih jenis identitas terlebih dahulu',
        identityPattern: '',
        identityMaxLength: '',
        identityHint: '',

        init() {
            // Set default payment method
            this.selectedPaymentMethod = '{{ old('payment_method', 'bank_transfer') }}';

            // Set default identity type if exists
            if (this.identityType) {
                this.updateIdentityPlaceholder();
            }

            // Add form validation
            this.setupFormValidation();
        },

        updateIdentityPlaceholder() {
            const identityConfigs = {
                'ktp': {
                    placeholder: 'Contoh: ****************',
                    pattern: '[0-9]{16}',
                    maxLength: '16',
                    hint: 'KTP harus 16 digit angka'
                },
                'sim': {
                    placeholder: 'Contoh: **************',
                    pattern: '[0-9]{12,14}',
                    maxLength: '14',
                    hint: 'SIM terdiri dari 12-14 digit angka'
                },
                'passport': {
                    placeholder: 'Contoh: A1234567',
                    pattern: '[A-Z][0-9]{7}',
                    maxLength: '8',
                    hint: 'Passport: 1 huruf kapital + 7 angka'
                },
                'kartu_pelajar': {
                    placeholder: 'Contoh: 2023001234',
                    pattern: '[0-9A-Za-z]{6,15}',
                    maxLength: '15',
                    hint: 'Nomor kartu pelajar sesuai sekolah'
                },
                'ktm': {
                    placeholder: 'Contoh: 20230112345',
                    pattern: '[0-9A-Za-z]{8,15}',
                    maxLength: '15',
                    hint: 'Nomor KTM sesuai universitas'
                },
                'kta': {
                    placeholder: 'Contoh: KTA001234',
                    pattern: '[A-Za-z0-9]{6,15}',
                    maxLength: '15',
                    hint: 'Nomor KTA sesuai organisasi'
                }
            };

            const config = identityConfigs[this.identityType];
            if (config) {
                this.identityPlaceholder = config.placeholder;
                this.identityPattern = config.pattern;
                this.identityMaxLength = config.maxLength;
                this.identityHint = config.hint;
            } else {
                this.identityPlaceholder = 'Pilih jenis identitas terlebih dahulu';
                this.identityPattern = '';
                this.identityMaxLength = '';
                this.identityHint = '';
            }
        },

        get subtotal() {
            return this.eventPrice * this.quantity;
        },

        get adminFee() {
            const fee = this.subtotal * 0.05;
            return Math.max(2500, Math.min(50000, fee));
        },

        get total() {
            return this.subtotal + this.adminFee;
        },

        increaseQuantity() {
            if (this.quantity < this.maxQuantity) {
                this.quantity++;
            }
        },

        decreaseQuantity() {
            if (this.quantity > 1) {
                this.quantity--;
            }
        },

        formatPrice(amount) {
            return 'Rp ' + new Intl.NumberFormat('id-ID').format(amount);
        },

        selectPaymentMethod(method) {
            this.selectedPaymentMethod = method;
            // Update radio button
            const radio = document.querySelector(`input[name="payment_method"][value="${method}"]`);
            if (radio) {
                radio.checked = true;
            }
        },

        validateForm() {
            const form = document.querySelector('form');
            const formData = new FormData(form);

            // Check required fields
            const requiredFields = [
                { name: 'attendee_name', label: 'Nama lengkap' },
                { name: 'attendee_email', label: 'Email' },
                { name: 'attendee_phone', label: 'Nomor telepon' },
                { name: 'attendee_gender', label: 'Jenis kelamin' },
                { name: 'attendee_birth_date', label: 'Tanggal lahir' },
                { name: 'identity_type', label: 'Jenis identitas' },
                { name: 'identity_number', label: 'Nomor identitas' },
                { name: 'payment_method', label: 'Metode pembayaran' },
                { name: 'terms_accepted', label: 'Persetujuan syarat dan ketentuan' }
            ];

            let isValid = true;
            let firstError = null;

            // Clear previous errors
            document.querySelectorAll('.error-message').forEach(el => el.remove());
            document.querySelectorAll('.border-red-500').forEach(el => {
                el.classList.remove('border-red-500');
                el.classList.add('border-gray-300');
            });

            requiredFields.forEach(field => {
                const value = formData.get(field.name);
                const element = document.querySelector(`[name="${field.name}"]`);

                if (!value || (field.name === 'terms_accepted' && value !== '1')) {
                    isValid = false;

                    if (element) {
                        // Add error styling
                        if (element.type !== 'checkbox' && element.type !== 'radio') {
                            element.classList.add('border-red-500');
                            element.classList.remove('border-gray-300');
                        }

                        // Add error message
                        const errorMsg = document.createElement('p');
                        errorMsg.className = 'error-message text-red-500 text-sm mt-1';
                        errorMsg.textContent = `${field.label} wajib diisi.`;

                        if (element.type === 'checkbox') {
                            element.closest('label').parentNode.appendChild(errorMsg);
                        } else if (element.type === 'radio') {
                            element.closest('.space-y-3').appendChild(errorMsg);
                        } else {
                            element.parentNode.appendChild(errorMsg);
                        }

                        if (!firstError) {
                            firstError = element;
                        }
                    }
                }
            });

            // Validate email format
            const email = formData.get('attendee_email');
            if (email && !this.isValidEmail(email)) {
                isValid = false;
                const emailElement = document.querySelector('[name="attendee_email"]');
                if (emailElement) {
                    emailElement.classList.add('border-red-500');
                    const errorMsg = document.createElement('p');
                    errorMsg.className = 'error-message text-red-500 text-sm mt-1';
                    errorMsg.textContent = 'Format email tidak valid.';
                    emailElement.parentNode.appendChild(errorMsg);

                    if (!firstError) {
                        firstError = emailElement;
                    }
                }
            }

            // Validate quantity
            if (this.quantity < 1 || this.quantity > this.maxQuantity) {
                isValid = false;
                this.showNotification(`Jumlah tiket harus antara 1 dan ${this.maxQuantity}.`, 'error');
            }

            if (!isValid && firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }

            return isValid;
        },

        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },

        handleSubmit(event) {
            event.preventDefault();

            if (this.loading) {
                return false;
            }

            // Validate form
            if (!this.validateForm()) {
                this.showNotification('Silakan lengkapi semua field yang wajib diisi.', 'error');
                return false;
            }

            // Additional custom validation
            if (!this.validateCustomFields()) {
                return false;
            }

            this.loading = true;

            // Show loading notification
            this.showNotification('Memproses pesanan...', 'info');

            // Play processing sound
            this.playNotificationSound('processing');

            // Submit form
            setTimeout(() => {
                event.target.submit();
            }, 500);
        },

        validateCustomFields() {
            let isValid = true;
            const errors = [];

            // Validate phone number
            const phone = document.getElementById('attendee_phone').value;
            if (phone && !phone.match(/^08[0-9]{8,11}$/)) {
                errors.push('Nomor WhatsApp harus dimulai dengan 08 dan terdiri dari 10-13 digit');
                isValid = false;
            }

            // Validate identity number based on type
            const identityType = this.identityType;
            const identityNumber = document.getElementById('identity_number').value;

            if (identityType && identityNumber) {
                const validationRules = {
                    'ktp': /^[0-9]{16}$/,
                    'sim': /^[0-9]{12,14}$/,
                    'passport': /^[A-Z][0-9]{7}$/,
                    'kartu_pelajar': /^[0-9A-Za-z]{6,15}$/,
                    'ktm': /^[0-9A-Za-z]{8,15}$/,
                    'kta': /^[A-Za-z0-9]{6,15}$/
                };

                if (validationRules[identityType] && !identityNumber.match(validationRules[identityType])) {
                    errors.push('Format nomor identitas tidak sesuai dengan jenis identitas yang dipilih');
                    isValid = false;
                }
            }

            // Show errors if any
            if (errors.length > 0) {
                this.showNotification(errors.join('<br>'), 'error');
                this.playNotificationSound('error');
            }

            return isValid;
        },

        playNotificationSound(type = 'info') {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);

                const soundConfig = {
                    'success': { frequency: 800, duration: 0.3 },
                    'error': { frequency: 300, duration: 0.5 },
                    'processing': { frequency: 600, duration: 0.2 },
                    'info': { frequency: 500, duration: 0.2 }
                };

                const config = soundConfig[type] || soundConfig.info;

                oscillator.frequency.setValueAtTime(config.frequency, audioContext.currentTime);
                oscillator.type = 'sine';

                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + config.duration);

                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + config.duration);

                // For success notifications, play multiple beeps
                if (type === 'success') {
                    setTimeout(() => this.playNotificationSound('info'), 200);
                    setTimeout(() => this.playNotificationSound('info'), 400);
                }

            } catch (error) {
                console.error('Error playing notification sound:', error);
            }
        },

        setupFormValidation() {
            // Real-time validation
            const form = document.querySelector('form');

            // Email validation
            const emailInput = document.querySelector('[name="attendee_email"]');
            if (emailInput) {
                emailInput.addEventListener('blur', () => {
                    const email = emailInput.value;
                    if (email && !this.isValidEmail(email)) {
                        emailInput.classList.add('border-red-500');
                        this.showFieldError(emailInput, 'Format email tidak valid.');
                    } else {
                        emailInput.classList.remove('border-red-500');
                        this.clearFieldError(emailInput);
                    }
                });
            }

            // Name validation
            const nameInput = document.querySelector('[name="attendee_name"]');
            if (nameInput) {
                nameInput.addEventListener('blur', () => {
                    if (!nameInput.value.trim()) {
                        nameInput.classList.add('border-red-500');
                        this.showFieldError(nameInput, 'Nama lengkap wajib diisi.');
                    } else {
                        nameInput.classList.remove('border-red-500');
                        this.clearFieldError(nameInput);
                    }
                });
            }
        },

        showFieldError(element, message) {
            this.clearFieldError(element);
            const errorMsg = document.createElement('p');
            errorMsg.className = 'field-error text-red-500 text-sm mt-1';
            errorMsg.textContent = message;
            element.parentNode.appendChild(errorMsg);
        },

        clearFieldError(element) {
            const existingError = element.parentNode.querySelector('.field-error');
            if (existingError) {
                existingError.remove();
            }
        },

        showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
            const icon = type === 'success' ? 'check-circle' : type === 'error' ? 'x-circle' : 'info';

            notification.className = `fixed top-4 right-4 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-x-full transition-transform duration-300`;
            notification.innerHTML = `
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        ${this.getIconPath(icon)}
                    </svg>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Animate out and remove
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        },

        getIconPath(icon) {
            const icons = {
                'check-circle': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>',
                'x-circle': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>',
                'info': '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>'
            };
            return icons[icon] || icons.info;
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers for payment method cards
    document.querySelectorAll('label[class*="relative"]').forEach(label => {
        const radio = label.querySelector('input[type="radio"]');
        if (radio && radio.name === 'payment_method') {
            label.addEventListener('click', function() {
                // Update Alpine.js data
                const alpineData = Alpine.$data(document.querySelector('[x-data]'));
                if (alpineData) {
                    alpineData.selectedPaymentMethod = radio.value;
                }
            });
        }
    });
});
</script>
@endpush
