# 🚀 TikPro Production Deployment Checklist

## ✅ Pre-Deployment Verification

### **1. Environment Setup**
- [ ] ✅ PHP 8.1+ with GD extension
- [ ] ✅ Laravel 10.48.29 framework
- [ ] ✅ MySQL/MariaDB database
- [ ] ✅ Composer dependencies installed
- [ ] ✅ Node.js & npm for assets (if needed)

### **2. Configuration Verification**
```bash
# Check PHP extensions
php -m | grep gd
# Should show: gd

# Check Laravel version
php artisan --version
# Should show: Laravel Framework 10.48.29

# Verify image configuration
php artisan config:show image
# Should show: driver: gd
```

### **3. Database Setup**
```bash
# Run migrations
php artisan migrate --force

# Seed database (if needed)
php artisan db:seed --force

# Verify database connection
php artisan db:show
```

### **4. Storage & Permissions**
```bash
# Create storage link
php artisan storage:link

# Set proper permissions (Linux/Mac)
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

# Verify storage directories exist
ls -la storage/app/public/
```

### **5. Cache & Optimization**
```bash
# Clear all caches
php artisan optimize:clear

# Cache for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize autoloader
composer install --optimize-autoloader --no-dev
```

## 🧪 **Testing Procedures**

### **Test 1: Application Health**
```bash
php artisan about
# Should show all green status
```

### **Test 2: Image Processing**
```bash
php artisan image:check-extensions
# Should show GD available, no Imagick errors
```

### **Test 3: Database Connectivity**
```bash
php artisan tinker --execute="
echo 'Testing database...' . PHP_EOL;
echo 'Users count: ' . \App\Models\User::count() . PHP_EOL;
echo 'Events count: ' . \App\Models\Event::count() . PHP_EOL;
"
```

### **Test 4: Purchase Flow Simulation**
```bash
php artisan tinker --execute="
echo 'Testing purchase flow...' . PHP_EOL;
try {
    \$user = \App\Models\User::first();
    \$event = \App\Models\Event::first();
    if (\$user && \$event) {
        echo 'Test data available' . PHP_EOL;
        echo 'User: ' . \$user->name . PHP_EOL;
        echo 'Event: ' . \$event->title . PHP_EOL;
    } else {
        echo 'No test data found' . PHP_EOL;
    }
} catch (Exception \$e) {
    echo 'Error: ' . \$e->getMessage() . PHP_EOL;
}
"
```

## 🔧 **Environment Configuration**

### **Production .env Settings**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

# Database
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tikpro_production
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

# Image Processing (CRITICAL)
IMAGE_DRIVER=gd
IMAGE_CACHE_ENABLED=true
IMAGE_CACHE_LIFETIME=3600
IMAGE_JPEG_QUALITY=85
IMAGE_PNG_QUALITY=9
IMAGE_WEBP_QUALITY=85

# Security
APP_KEY=your-32-character-secret-key
SESSION_DRIVER=database
QUEUE_CONNECTION=database

# Mail
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
```

## 🌐 **Web Server Configuration**

### **Apache Virtual Host**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/tikpro/public
    
    <Directory /path/to/tikpro/public>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/tikpro_error.log
    CustomLog ${APACHE_LOG_DIR}/tikpro_access.log combined
</VirtualHost>
```

### **Nginx Configuration**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/tikpro/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }

    location ~ /\.ht {
        deny all;
    }
}
```

## 📊 **Performance Monitoring**

### **Key Metrics to Monitor**
1. **Response Time**: < 2 seconds for page loads
2. **Memory Usage**: Monitor PHP memory consumption
3. **Database Queries**: Optimize slow queries
4. **Error Rates**: Monitor application errors
5. **Image Processing**: Monitor GD performance

### **Monitoring Commands**
```bash
# Check application performance
php artisan route:list --compact

# Monitor logs
tail -f storage/logs/laravel.log

# Check queue status (if using)
php artisan queue:work --verbose

# Monitor database
php artisan db:monitor
```

## 🔒 **Security Checklist**

### **Application Security**
- [ ] ✅ APP_DEBUG=false in production
- [ ] ✅ Strong APP_KEY generated
- [ ] ✅ Database credentials secured
- [ ] ✅ File permissions properly set
- [ ] ✅ HTTPS enabled
- [ ] ✅ CSRF protection enabled
- [ ] ✅ SQL injection protection (Eloquent ORM)

### **Server Security**
- [ ] ✅ Firewall configured
- [ ] ✅ SSH key authentication
- [ ] ✅ Regular security updates
- [ ] ✅ Backup strategy implemented
- [ ] ✅ SSL certificate installed

## 📱 **PWA Features Verification**

### **PWA Checklist**
```bash
# Publish PWA assets
php artisan vendor:publish --provider="Kangpcode\LaravelPwa\PWAServiceProvider"

# Verify PWA files
ls -la public/
# Should show: manifest.json, sw.js
```

### **PWA Testing**
1. **Manifest**: Check `/manifest.json` loads correctly
2. **Service Worker**: Verify `/sw.js` is accessible
3. **Icons**: Ensure PWA icons are in place
4. **Offline**: Test offline functionality

## 🚀 **Deployment Steps**

### **Step 1: Code Deployment**
```bash
# Clone/update repository
git clone https://github.com/your-repo/tikpro.git
cd tikpro

# Install dependencies
composer install --optimize-autoloader --no-dev
npm install && npm run build
```

### **Step 2: Environment Setup**
```bash
# Copy environment file
cp .env.example .env
# Edit .env with production settings

# Generate application key
php artisan key:generate
```

### **Step 3: Database Setup**
```bash
# Run migrations
php artisan migrate --force

# Seed initial data
php artisan db:seed --force
```

### **Step 4: Optimization**
```bash
# Cache everything
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Create storage link
php artisan storage:link
```

### **Step 5: Final Verification**
```bash
# Test application
php artisan about
php artisan image:check-extensions

# Start queue worker (if using)
php artisan queue:work --daemon
```

## 🔄 **Maintenance Procedures**

### **Daily Tasks**
```bash
# Check application health
php artisan about

# Monitor logs
tail -n 100 storage/logs/laravel.log

# Check disk space
df -h
```

### **Weekly Tasks**
```bash
# Clear old logs
php artisan log:clear

# Optimize database
php artisan model:prune

# Update dependencies (staging first)
composer update
```

### **Monthly Tasks**
```bash
# Security updates
apt update && apt upgrade

# Backup verification
# Test restore procedures

# Performance review
# Analyze slow queries
```

## 📞 **Support & Troubleshooting**

### **Common Issues & Solutions**

#### **Issue 1: Image Processing Errors**
```bash
# Check GD extension
php -m | grep gd

# Verify configuration
php artisan config:show image

# Test image processing
php artisan image:test-processing
```

#### **Issue 2: Database Connection**
```bash
# Test connection
php artisan db:show

# Check credentials
php artisan config:show database
```

#### **Issue 3: Permission Issues**
```bash
# Fix storage permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache
```

### **Emergency Procedures**
1. **Application Down**: Check web server and PHP-FPM
2. **Database Issues**: Verify connection and credentials
3. **High Load**: Enable maintenance mode: `php artisan down`
4. **Security Breach**: Change APP_KEY and passwords immediately

---

**Deployment Status**: 🚀 **READY FOR PRODUCTION**  
**Last Updated**: December 2024  
**Version**: TikPro v1.0 - Stable Release  
**Support**: Full documentation and troubleshooting guides available
